import { useState } from 'react';
import { Trophy, Medal, Award, Crown, Star, Users, Flame } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose
} from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { useEffect } from 'react';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';
import { supabase } from '@/integrations/supabase/client';

interface LeaderboardUser {
  id: string;
  displayName: string;
  username: string;
  photoURL: string;
  todayHours: number;
  totalHours: number;
  weeklyHours: number;
  level: number;
  streak: number;
}

export function LeaderboardButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [leaderboardType, setLeaderboardType] = useState<'daily' | 'weekly'>('daily');
  const [leaderboardUsers, setLeaderboardUsers] = useState<LeaderboardUser[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useSupabaseAuth();

  const fetchLeaderboardData = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Get today's date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];

      // Calculate the start of current week (Sunday)
      const now = new Date();
      const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - dayOfWeek);
      startOfWeek.setHours(0, 0, 0, 0);

      // Convert to YYYY-MM-DD format
      const weekStart = startOfWeek.toISOString().split('T')[0];

      // Query all users from Supabase
      const { data: users, error } = await supabase
        .from('users')
        .select('id, username, display_name, photo_url, stats, progress');

      if (error) {
        console.error('Error fetching users:', error);
        return;
      }

      const leaderboardData: LeaderboardUser[] = [];

      // Process each user's data
      for (const userData of users || []) {
        // Skip users without necessary data
        if (!userData.progress?.studySessions) continue;

        const sessions = Object.values(userData.progress.studySessions || {}) as any[];

        // Filter out manual sessions for fairness
        const automaticSessions = sessions.filter((session: any) => !session.isManualEntry);
        // Filter out sessions longer than 7 hours (25200 seconds)
        const filteredAutomaticSessions = automaticSessions.filter((session: any) => (session.duration || 0) <= 25200);

        // Calculate total study hours from automatic sessions only
        const totalHours = filteredAutomaticSessions.reduce((total: number, session: any) =>
          total + (session.duration || 0) / 3600, 0);

        // Calculate today's study hours from automatic sessions only
        const todaySessions = filteredAutomaticSessions.filter((session: any) => session.date === today);
        const todayHours = todaySessions.reduce((total: number, session: any) =>
          total + (session.duration || 0) / 3600, 0);

        // Calculate this week's study hours
        const weekSessions = filteredAutomaticSessions.filter((session: any) =>
          session.date >= weekStart && session.date <= today);
        const weeklyHours = weekSessions.reduce((total: number, session: any) =>
          total + (session.duration || 0) / 3600, 0);

        // Get streak from user data or calculate it
        const streak = userData.stats?.currentStreak || 0;

        // Get user level from gamification data or default to 1
        const level = userData.stats?.level || 1;

        leaderboardData.push({
          id: userData.id,
          displayName: userData.display_name || 'Anonymous',
          username: userData.username || 'user',
          photoURL: userData.photo_url || '',
          todayHours,
          totalHours,
          weeklyHours,
          level,
          streak
        });
      }

      // Sort based on leaderboard type
      let sortedData: LeaderboardUser[] = [];

      if (leaderboardType === 'daily') {
        // Filter users who have studied at least 30 minutes today (0.5 hours)
        const filteredData = leaderboardData.filter(user => user.todayHours >= 0.5);
        sortedData = filteredData.sort((a, b) => b.todayHours - a.todayHours);
      } else if (leaderboardType === 'weekly') {
        // Show all users who studied more than 1 hour this week
        sortedData = leaderboardData
          .filter(user => user.weeklyHours >= 1)
          .sort((a, b) => b.weeklyHours - a.weeklyHours);
      }

      setLeaderboardUsers(sortedData);
    } catch (error) {
      console.error('Error fetching leaderboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchLeaderboardData();
    }
  }, [isOpen, leaderboardType]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 0:
        return <Crown className="h-5 w-5 text-yellow-500" />;
      case 1:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 2:
        return <Award className="h-5 w-5 text-amber-700" />;
      default:
        return <Star className="h-5 w-5 text-purple-500 opacity-50" />;
    }
  };

  // Find the current user in the leaderboard
  const currentUserRank = leaderboardUsers.findIndex(u => u.id === user?.id);

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="fixed z-50 bottom-28 right-8 h-12 w-12 rounded-full shadow-lg border dark:border-slate-800 bg-background/80 backdrop-blur-md hover:bg-primary/20 dark:hover:bg-white/10 leaderboard-trigger-button"
        onClick={() => setIsOpen(true)}
      >
        <Trophy className="h-5 w-5 text-primary" />
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-lg max-h-[90vh] bg-gradient-to-b from-purple-50 to-indigo-50 dark:from-slate-900 dark:to-slate-950 border-purple-200 dark:border-purple-900/50 shadow-[0_0_50px_-12px] shadow-purple-500/30">
          <DialogHeader className="py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Trophy className="h-6 w-6 text-yellow-500" />
                <DialogTitle className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-indigo-600 dark:from-purple-400 dark:to-indigo-400">
                  Study Leaderboard
                </DialogTitle>
              </div>
              <DialogClose className="rounded-full p-2 hover:bg-slate-200 dark:hover:bg-slate-800" />
            </div>
            <DialogDescription className="pt-2 text-center">
              Challenge yourself and climb the ranks! 🚀
            </DialogDescription>
          </DialogHeader>

          <Tabs
            defaultValue="daily"
            value={leaderboardType}
            onValueChange={(value) => setLeaderboardType(value as 'daily' | 'weekly')}
            className="w-full mt-2"
          >
            <TabsList className="grid grid-cols-2 mb-4 bg-slate-100/50 dark:bg-slate-900/50">
              <TabsTrigger value="daily" className="data-[state=active]:bg-purple-500 data-[state=active]:text-white">
                Today
              </TabsTrigger>
              <TabsTrigger value="weekly" className="data-[state=active]:bg-purple-500 data-[state=active]:text-white">
                This Week
              </TabsTrigger>
            </TabsList>

            <div className="bg-white/30 dark:bg-slate-800/30 rounded-lg border border-purple-100/50 dark:border-purple-900/50 p-4 mb-2">
              {isLoading ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
                </div>
              ) : (
                <ScrollArea className="h-[40vh]">
                  <div className="space-y-3 pr-4">
                    {leaderboardUsers.length > 0 ? (
                      leaderboardUsers.map((leaderboardUser, index) => (
                        <div 
                          key={leaderboardUser.id} 
                          className={cn(
                            "flex items-center justify-between p-3 rounded-lg transition-all",
                            leaderboardUser.id === user?.id
                              ? "bg-purple-500/10 border border-purple-500/30"
                              : "bg-white/50 dark:bg-slate-900/50 hover:bg-slate-100/70 dark:hover:bg-slate-800/70"
                          )}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center justify-center w-8 h-8">
                              {getRankIcon(index)}
                            </div>
                            <div 
                              className="w-8 h-8 rounded-full bg-cover bg-center"
                              style={{ 
                                backgroundImage: leaderboardUser.photoURL 
                                  ? `url(${leaderboardUser.photoURL})` 
                                  : `url(https://ui-avatars.com/api/?name=${encodeURIComponent(leaderboardUser.username)}&background=random)` 
                              }}
                            />
                            <div className="ml-1">
                              <p className="font-medium text-sm">@{leaderboardUser.username}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold">
                              {leaderboardType === 'daily' && `${leaderboardUser.todayHours.toFixed(1)}h`}
                              {leaderboardType === 'weekly' && `${leaderboardUser.weeklyHours.toFixed(1)}h`}
                            </p>
                            <div className="flex items-center text-xs">
                              <Progress 
                                value={leaderboardType === 'daily' ? Math.min(leaderboardUser.todayHours * 10, 100) : Math.min(leaderboardUser.weeklyHours, 100)} 
                                className="h-1.5 w-16" 
                              />
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <Users className="h-12 w-12 mx-auto mb-2 opacity-30" />
                        <p>No data available yet</p>
                        <p className="text-sm">Start studying to get on the leaderboard!</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              )}
            </div>

            {currentUserRank !== -1 ? (
              <div className="bg-purple-100 dark:bg-purple-900/20 p-2 rounded-md">
                <p className="text-center text-sm">
                  You're <span className="font-bold">#{currentUserRank + 1}</span> on the leaderboard! 🎉
                </p>
              </div>
            ) : (
              <div className="bg-purple-100 dark:bg-purple-900/20 p-2 rounded-md">
                <p className="text-center text-sm">
                  Study today to appear on the leaderboard! 💪
                </p>
              </div>
            )}
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  );
} 