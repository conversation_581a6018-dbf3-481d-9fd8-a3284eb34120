import React, { useState, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, CheckCircle2, Award, Star, Trophy, BookOpen } from 'lucide-react'; // Added BookOpen icon
import { But<PERSON> } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"; // Import Tooltip components
import { cn } from "@/lib/utils"; // Import utility function for conditional classes

export interface DailyStat { // Added export
  date: string; // "YYYY-MM-DD"
  totalDuration: number; // seconds
  subjectDurations: { [key: string]: number };
  completedPomodoros: number;
}

interface StudyCalendarProps {
  dailyStats: DailyStat[];
  formatDuration: (seconds: number) => string; // Function to format seconds into HH:MM or similar
  onDayClick?: (date: Date, studyData: DailyStat | undefined) => void; // Optional click handler
  streakData?: { [date: string]: boolean }; // Optional streak data
  dailyTargetSeconds?: number; // Optional daily target in seconds
  subjectColorMap?: { [subject: string]: string }; // Optional subject color map
}

const StudyCalendar: React.FC<StudyCalendarProps> = ({
  dailyStats,
  formatDuration,
  onDayClick,
  streakData = {},
  dailyTargetSeconds = 0, // Default target to 0 if not provided
  subjectColorMap = {} // Default color map
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  const daysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getDayOfWeek = (year: number, month: number, day: number): number => {
    // Returns 0 for Sunday, 1 for Monday, etc.
   return new Date(year, month, day).getDay();
 };

// Helper function to format Date object to YYYY-MM-DD in local time
const formatDateToLocalYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

 const getStudyDataForDay = (date: Date): DailyStat | undefined => {
    const dateString = formatDateToLocalYYYYMMDD(date); // Use local date format instead of UTC
    return dailyStats.find(stat => stat.date === dateString);
  };

  // Updated background color logic for theme compatibility
  const getDayBackgroundColor = (duration: number | undefined, isFuture: boolean): string => {
    const baseOpacity = isFuture ? 'opacity-30' : 'opacity-100';
    // Use muted for no study, primary with varying opacity for study time
    if (duration === undefined || duration <= 0) return `bg-muted/30 hover:bg-muted/60 ${baseOpacity}`;
    const hours = duration / 3600;
    if (hours < 2) return `bg-primary/20 hover:bg-primary/40 ${baseOpacity}`;
    if (hours < 4) return `bg-primary/40 hover:bg-primary/60 ${baseOpacity}`;
    if (hours < 6) return `bg-primary/60 hover:bg-primary/80 ${baseOpacity}`;
    return `bg-primary/80 hover:bg-primary/95 ${baseOpacity}`;
  };

  const formatShortDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.floor(minutes % 60);
    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`;
    }
    if (remainingMinutes > 0) {
      return `${remainingMinutes}m`;
    }
    // Only show <1m if there was *some* duration but less than 1 minute
    if (minutes > 0) {
        return `<1m`;
    }
    return ''; // Return empty if 0 minutes
  };

  const handlePrevMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth(); // 0-indexed
  const numDays = daysInMonth(year, month);
  const firstDayOfWeek = getDayOfWeek(year, month, 1); // 0=Sun, 1=Mon,...

  const calendarDays = useMemo(() => {
    const days: (Date | null)[] = [];
    // Add empty cells for days before the 1st of the month
    for (let i = 0; i < firstDayOfWeek; i++) {
      days.push(null);
    }
    // Add days of the month
    for (let day = 1; day <= numDays; day++) {
      days.push(new Date(year, month, day));
    }
    return days;
  }, [year, month, numDays, firstDayOfWeek]);

  const today = new Date();
  today.setHours(0, 0, 0, 0); // Normalize today for date comparisons

  const isToday = (date: Date | null): boolean => {
    if (!date) return false;
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };

  // Helper function to check if a date is strictly in the future
  const isFutureDate = (date: Date | null): boolean => {
    if (!date) return false;
    const checkDate = new Date(date); // Create a copy to avoid modifying the original
    checkDate.setHours(0, 0, 0, 0); // Normalize the date being checked
    return checkDate > today;
  };

  // Determine if the next month button should be disabled
  const isNextMonthDisabled = useMemo(() => {
    const nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
    nextMonth.setHours(0, 0, 0, 0); // Normalize next month start
    // Disable if the *start* of the next month is after today
    return nextMonth > today;
  }, [currentDate, today]);

  // Add CSS keyframes and animation classes for achievement effects
  // These will be used for the achievement indicator
  const achievementAnimationClass = "animate-achievement";
  const achievementGlowClass = "achievement-glow";

  return (
    <Card className="bg-card text-card-foreground border-none shadow-xl rounded-xl overflow-hidden md:col-span-2">
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes pulse-scale {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }
        
        @keyframes achievement-shine {
          0% { background-position: 200% center; }
          100% { background-position: -200% center; }
        }
        
        .animate-achievement {
          animation: pulse-scale 2s ease-in-out infinite;
        }
        
        .achievement-glow {
          background: linear-gradient(90deg, 
            rgba(251, 191, 36, 0) 0%, 
            rgba(251, 191, 36, 0.4) 25%, 
            rgba(245, 158, 11, 0.4) 50%, 
            rgba(251, 191, 36, 0.4) 75%, 
            rgba(251, 191, 36, 0) 100%);
          background-size: 200% auto;
          animation: achievement-shine 4s linear infinite;
        }
        
        @keyframes gentle-float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-3px); }
        }
        
        .float-animation {
          animation: gentle-float 3s ease-in-out infinite;
        }
      `}} />

      <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/5 pb-5 pt-6 px-6 border-b border-border/40">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <CardTitle className="text-xl font-medium flex items-center gap-2.5">
            <div className="bg-primary/10 p-2 rounded-lg float-animation">
              <BookOpen className="h-5 w-5 text-primary" />
            </div>
            <span>Study Calendar</span>
          </CardTitle>
          
          <div className="flex items-center gap-3 bg-background/80 backdrop-blur-sm rounded-lg p-1.5 shadow-sm border border-border/30">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={handlePrevMonth} 
              className="h-8 w-8 rounded-md text-muted-foreground hover:text-foreground hover:bg-primary/10"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <span className="text-foreground font-medium px-2">
              {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
            </span>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={handleNextMonth}
              className="h-8 w-8 rounded-md text-muted-foreground hover:text-foreground hover:bg-primary/10 disabled:opacity-40 disabled:cursor-not-allowed"
              disabled={isNextMonthDisabled}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-6 pb-6 px-6">
        <div className="grid grid-cols-7 gap-2 mb-4">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, i) => (
            <div 
              key={day} 
              className={cn(
                "text-center text-xs font-medium py-1 rounded-md",
                i === 0 || i === 6 ? "text-rose-500/70 dark:text-rose-400/70" : "text-foreground/70"
              )}
            >
              {day}
            </div>
          ))}
        </div>
        <TooltipProvider delayDuration={200}>
          <div className="grid grid-cols-7 gap-2">
            {calendarDays.map((date, index) => {
              const isFuture = isFutureDate(date);
              const studyData = date ? getStudyDataForDay(date) : undefined;
              const bgColor = getDayBackgroundColor(studyData?.totalDuration, isFuture);
              const isCurrentDay = isToday(date);
              const showDayOff = !isFuture && (!studyData || studyData.totalDuration <= 0);
              const dateString = date ? formatDateToLocalYYYYMMDD(date) : '';
              const isStreakDay = date ? streakData[dateString] === true : false;
              const goalMet = date && !isFuture && studyData && dailyTargetSeconds > 0 && studyData.totalDuration >= dailyTargetSeconds;
              
              // Calculate percentage of goal achieved to visualize progress
              const progressPercent = date && !isFuture && studyData && dailyTargetSeconds > 0 
                ? Math.min(100, Math.round((studyData.totalDuration / dailyTargetSeconds) * 100)) 
                : 0;
              
              // Calculate overshoot - how much over the goal the user achieved
              const goalOvershoot = date && studyData && dailyTargetSeconds > 0 && studyData.totalDuration > dailyTargetSeconds
                ? Math.round((studyData.totalDuration - dailyTargetSeconds) / 60) // minutes over goal
                : 0;

              // Construct the detailed tooltip content
              let detailedTooltipContent: React.ReactNode = null;
              if (studyData && studyData.totalDuration > 0) {
                  const subjects = Object.entries(studyData.subjectDurations)
                      .filter(([, duration]) => duration > 0) // Only show subjects with time > 0
                      .sort((a, b) => b[1] - a[1]) // Sort by duration in descending order
                      .map(([subjectName, duration]) => {
                          const color = subjectColorMap[subjectName] || '#a1a1aa';
                          return (
                              <li key={subjectName} className="text-xs flex items-center gap-1.5">
                                  <span className="h-2 w-2 rounded-full inline-block" style={{ backgroundColor: color }}></span>
                                  <span>{subjectName}: {formatDuration(duration)}</span>
                              </li>
                          );
                      });

                  detailedTooltipContent = (
                      <div className="space-y-1">
                          <p className="font-semibold">Total: {formatDuration(studyData.totalDuration)}</p>
                          {subjects.length > 0 && (
                              <div>
                                  <p className="text-xs font-medium text-white/80 mb-0.5">Subjects:</p>
                                  <ul className="list-disc list-inside pl-1 space-y-0.5">
                                      {subjects}
                                  </ul>
                              </div>
                          )}
                          {studyData.completedPomodoros > 0 && (
                              <p className="text-xs">Pomodoros: {studyData.completedPomodoros}</p>
                          )}
                      </div>
                  );
              } else if (showDayOff) {
                  detailedTooltipContent = <p>Day Off</p>;
              }

              return (
                <Tooltip key={index} open={date && detailedTooltipContent ? undefined : false}>
                  <TooltipTrigger asChild>
                    <div
                      onClick={() => date && onDayClick ? onDayClick(date, studyData) : undefined}
                      className={cn(
                        `relative h-20 rounded-md p-1.5 text-xs flex flex-col justify-between transition-all duration-200 ease-in-out ${bgColor}`,
                        date && onDayClick ? 'cursor-pointer hover:ring-2 hover:ring-offset-1 hover:ring-offset-background hover:ring-primary' : date ? 'cursor-default' : 'opacity-40 cursor-default',
                        isCurrentDay ? 'ring-2 ring-offset-2 ring-offset-background ring-primary shadow-lg' : '',
                        isStreakDay && !isCurrentDay ? 'border-2 border-emerald-500/70' : '',
                        goalMet ? 'bg-gradient-to-br from-amber-50/40 to-amber-100/40 dark:from-amber-900/10 dark:to-amber-800/10 shadow-md' : ''
                      )}
                    >
                      {/* Goal Progress Indicator for visible goals */}
                      {date && !isFuture && dailyTargetSeconds > 0 && studyData?.totalDuration > 0 && progressPercent > 0 && (
                        <div 
                          className={cn(
                            "absolute bottom-0 left-0 right-0 h-1 bg-amber-200/30 dark:bg-amber-700/20 rounded-b-md overflow-hidden",
                            goalMet ? "bg-amber-200/50 dark:bg-amber-700/30" : ""
                          )}
                        >
                          <div 
                            className={cn(
                              "h-full rounded-b-md", 
                              goalMet 
                                ? "bg-gradient-to-r from-amber-300 to-amber-500 dark:from-amber-400/70 dark:to-amber-600/70"
                                : "bg-amber-300/50 dark:bg-amber-600/40"
                            )}
                            style={{ width: `${progressPercent}%` }}
                          />
                        </div>
                      )}
                      
                      {/* Streak Indicator */}
                      {isStreakDay && (
                        <span className="absolute top-1 right-1 text-emerald-400 opacity-70" title="Streak Day">
                          🔥
                        </span>
                      )}
                      
                      {/* Enhanced Goal Met Indicator */}
                      {goalMet && (
                        <div 
                          className={cn(
                            "absolute top-1 left-1 p-0.5",
                            achievementAnimationClass
                          )} 
                          title={`Daily Goal Met! ${goalOvershoot > 0 ? `(${goalOvershoot}m over target)` : ''}`}
                        >
                          <div className={cn(
                            "rounded-full size-4 bg-gradient-to-r from-amber-300 to-amber-500 dark:from-amber-400 dark:to-amber-600 flex items-center justify-center shadow-md",
                            achievementGlowClass
                          )}>
                            <Trophy className="h-2.5 w-2.5 text-white dark:text-amber-100" strokeWidth={3} />
                          </div>
                        </div>
                      )}

                      {date ? (
                        <>
                          {/* Day number with enhanced position for achievement days */}
                          <span className={`font-medium text-xs sm:text-sm ${isCurrentDay ? 'text-primary font-bold' : 'text-foreground/80'} ${goalMet ? 'ml-5' : ''}`}>
                            {date.getDate()}
                          </span>
                          
                          {/* Centered time with enhanced styling for goal met */}
                          <div className="text-center mt-auto mb-1">
                            {studyData && studyData.totalDuration > 0 ? (
                              <span className={cn(
                                "font-semibold text-sm leading-tight block",
                                goalMet ? "text-amber-700 dark:text-amber-300" : "text-foreground"
                              )}>
                                {formatShortDuration(studyData.totalDuration)}
                                {goalMet && (
                                  <span className="text-xs block text-emerald-600/90 dark:text-emerald-400/90 font-medium">
                                    Goal Complete!
                                  </span>
                                )}
                              </span>
                            ) : (
                              showDayOff && <span className="text-muted-foreground/80 text-sm leading-tight block">Day Off</span>
                            )}
                          </div>
                        </>
                      ) : (
                        <span /> // Empty cell
                      )}
                    </div>
                  </TooltipTrigger>
                  {detailedTooltipContent && (
                    <TooltipContent side="top" align="center" className="bg-popover text-popover-foreground border-border shadow-lg rounded-md px-3 py-2 text-sm">
                      {/* Enhanced Target Achieved Indicator in Tooltip */}
                      {goalMet && (
                        <div className="mb-2 flex flex-col items-center gap-1">
                          <div className="flex items-center gap-1.5 text-amber-500 dark:text-amber-400">
                            <Trophy className="h-4 w-4" />
                            <span className="text-sm font-semibold">Daily Target Achieved!</span>
                          </div>
                          {goalOvershoot > 0 && (
                            <span className="text-xs text-emerald-600 dark:text-emerald-400">
                              {goalOvershoot}m over your daily goal
                            </span>
                          )}
                        </div>
                      )}
                      {/* Render the constructed content */}
                      {detailedTooltipContent}
                    </TooltipContent>
                  )}
                </Tooltip>
              );
            })}
          </div>
        </TooltipProvider>
      </CardContent>
    </Card>
  );
};

export default StudyCalendar;
