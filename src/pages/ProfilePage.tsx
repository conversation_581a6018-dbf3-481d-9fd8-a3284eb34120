import { useState, useEffect, useRef, useMemo } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, User, Edit2, Camera, MapPin, Activity, Award, Calendar, MessageSquare, RefreshCw, X, CheckCircle2, Save, Clock, Timer, BookOpen, Bar<PERSON>hart, <PERSON>hart, <PERSON>hart } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import {
  getExtendedUserProfile,
  updateUserProfile,
  updateUserProfilePicture,
  updateUserBackgroundImage,
  checkUsernameAvailability
} from "../utils/supabase";
import { useSupabaseUserStore } from "../stores/supabaseUserStore";

// Analytics related interfaces
interface StudySession {
  subject: string
  duration: number
  mode: "pomodoro" | "stopwatch"
  phase: "work" | "shortBreak" | "longBreak"
  completed: boolean
  date: string
  weekNumber: number
  month: string
  year: number
  startTime?: Date
  endTime?: Date
  taskName?: string
  taskType: string
  taskDescription?: string
  focusRating?: "focused" | "neutral" | "distracted"
  notes?: string
  subjectColor?: string
  productivityRating?: number
}

interface Analytics {
  dailyStats: {
    date: string
    totalDuration: number
    subjectDurations: { [key: string]: number }
    completedPomodoros: number
    taskTypeDurations?: { [key: string]: number }
  }[]
  weeklyStats: {
    weekNumber: number
    year: number
    totalDuration: number
    subjectDurations: { [key: string]: number }
    completedPomodoros: number
    taskTypeDurations?: { [key: string]: number }
  }[]
  monthlyStats: {
    month: string
    year: number
    monthKey: string
    totalDuration: number
    subjectDurations: { [key: string]: number }
    completedPomodoros: number
    taskTypeDurations?: { [key: string]: number }
  }[]
  subjectStats: {
    subject: string
    totalDuration: number
    completedPomodoros: number
    averageSessionDuration: number
  }[]
  taskTypeStats?: {
    taskType: string
    totalDuration: number
    sessionCount: number
    averageSessionDuration: number
    averageProductivityRating?: number
  }[]
}

interface UserStats {
  totalStudyTime?: number
  studyStreak?: number
  questionsAsked?: number
  questionsAnswered?: number
  [key: string]: any
}

const COLORS = ['#6366f1', '#8b5cf6', '#ec4899', '#f43f5e', '#f97316', '#10b981', '#06b6d4', '#3b82f6']

const ProfilePage = () => {
  const { user } = useSupabaseAuth();
  const { updateUserProfile: updateUserProfileStore } = useSupabaseUserStore();
  const navigate = useNavigate();
  const [username, setUsername] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [editMode, setEditMode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [profilePicFile, setProfilePicFile] = useState<File | null>(null);
  const [backgroundImageFile, setBackgroundImageFile] = useState<File | null>(null);
  const [backgroundImage, setBackgroundImage] = useState<string>("");
  const [bio, setBio] = useState<string>("");
  const [location, setLocation] = useState<string>("");
  const [isUploading, setIsUploading] = useState<{profile: boolean, background: boolean}>({
    profile: false,
    background: false
  });
  const [extendedProfile, setExtendedProfile] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [profilePicKey, setProfilePicKey] = useState<number>(Date.now()); // State for cache busting profile picture
  
  // Analytics related state
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [subjectColorMap, setSubjectColorMap] = useState<{ [subject: string]: string }>({});
  const [streakInfo, setStreakInfo] = useState<{
    currentStreak: number;
    longestStreak: number;
    streakMap: { [date: string]: boolean };
  }>({ currentStreak: 0, longestStreak: 0, streakMap: {} });
  const [isAnalyticsLoading, setIsAnalyticsLoading] = useState(true);
  
  // Form state for edit mode
  const [editForm, setEditForm] = useState({
    username: "",
    displayName: "",
    bio: "",
    location: ""
  });
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);

  const profilePicInputRef = useRef<HTMLInputElement>(null);
  const backgroundImageInputRef = useRef<HTMLInputElement>(null);

  // Helper functions for analytics
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = Math.round(minutes % 60)

    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`
    }
    if (remainingMinutes > 0) {
      return `${remainingMinutes}m`
    }
    return `0m`
  }
  
  // Helper function to format Date object to YYYY-MM-DD in local time
  const formatDateToLocalYYYYMMDD = (date: Date): string => {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  
  // Function to process analytics from study sessions
  const processAnalytics = (sessions: StudySession[]): Analytics => {
    const dailyStats: { [key: string]: any } = {}
    const weeklyStats: { [key: string]: any } = {}
    const monthlyStats: { [key: string]: any } = {}
    const subjectStats: { [key: string]: any } = {}
    const taskTypeStats: { [key: string]: any } = {}

    sessions.forEach(session => {
      // Skip invalid sessions
      if (!session.duration || session.duration < 0 || !session.date || !session.subject) return

      // Extract task type with fallback to "Study"
      const taskType = session.taskType || "Study"

      // Daily stats
      if (!dailyStats[session.date]) {
        dailyStats[session.date] = {
          date: session.date,
          totalDuration: 0,
          subjectDurations: {},
          taskTypeDurations: {},
          completedPomodoros: 0
        }
      }
      dailyStats[session.date].totalDuration += session.duration
      dailyStats[session.date].subjectDurations[session.subject] =
        (dailyStats[session.date].subjectDurations[session.subject] || 0) + session.duration
      dailyStats[session.date].taskTypeDurations[taskType] =
        (dailyStats[session.date].taskTypeDurations[taskType] || 0) + session.duration
      if (session.completed && session.mode === "pomodoro") {
        dailyStats[session.date].completedPomodoros++
      }

      // Weekly stats
      const weekKey = `${session.year}-W${session.weekNumber}`
      if (!weeklyStats[weekKey]) {
        weeklyStats[weekKey] = {
          weekNumber: session.weekNumber,
          year: session.year,
          totalDuration: 0,
          subjectDurations: {},
          taskTypeDurations: {},
          completedPomodoros: 0
        }
      }
      weeklyStats[weekKey].totalDuration += session.duration
      weeklyStats[weekKey].subjectDurations[session.subject] =
        (weeklyStats[weekKey].subjectDurations[session.subject] || 0) + session.duration
      weeklyStats[weekKey].taskTypeDurations[taskType] =
        (weeklyStats[weekKey].taskTypeDurations[taskType] || 0) + session.duration
      if (session.completed && session.mode === "pomodoro") {
        weeklyStats[weekKey].completedPomodoros++
      }

      // Monthly stats
      const monthKey = `${session.year}-${session.month}`;
      if (!monthlyStats[monthKey]) {
        monthlyStats[monthKey] = {
          month: session.month, 
          year: session.year,
          monthKey: monthKey,
          totalDuration: 0,
          subjectDurations: {},
          taskTypeDurations: {},
          completedPomodoros: 0
        }
      }
      monthlyStats[monthKey].totalDuration += session.duration
      monthlyStats[monthKey].subjectDurations[session.subject] =
        (monthlyStats[monthKey].subjectDurations[session.subject] || 0) + session.duration
      monthlyStats[monthKey].taskTypeDurations[taskType] =
        (monthlyStats[monthKey].taskTypeDurations[taskType] || 0) + session.duration
      if (session.completed && session.mode === "pomodoro") {
        monthlyStats[monthKey].completedPomodoros++
      }

      // Subject stats
      if (!subjectStats[session.subject]) {
        subjectStats[session.subject] = {
          subject: session.subject,
          totalDuration: 0,
          completedPomodoros: 0,
          sessionCount: 0
        }
      }
      subjectStats[session.subject].totalDuration += session.duration
      subjectStats[session.subject].sessionCount++
      if (session.completed && session.mode === "pomodoro") {
        subjectStats[session.subject].completedPomodoros++
      }

      // Task type stats
      if (!taskTypeStats[taskType]) {
        taskTypeStats[taskType] = {
          taskType: taskType,
          totalDuration: 0,
          sessionCount: 0,
          productivityRatings: []
        }
      }
      taskTypeStats[taskType].totalDuration += session.duration
      taskTypeStats[taskType].sessionCount++

      // If this session has a productivity rating, add it to the array
      if (session.productivityRating && session.productivityRating > 0) {
        taskTypeStats[taskType].productivityRatings.push(session.productivityRating)
      }
    })

    // Calculate averages for subject stats
    Object.values(subjectStats).forEach((stats: any) => {
      stats.averageSessionDuration = stats.sessionCount > 0 ? stats.totalDuration / stats.sessionCount : 0;
    })

    // Calculate averages for task type stats
    Object.values(taskTypeStats).forEach((stats: any) => {
      stats.averageSessionDuration = stats.sessionCount > 0 ? stats.totalDuration / stats.sessionCount : 0;

      // Calculate average productivity rating if there are any ratings
      if (stats.productivityRatings && stats.productivityRatings.length > 0) {
        const sum = stats.productivityRatings.reduce((a: number, b: number) => a + b, 0);
        stats.averageProductivityRating = Math.round((sum / stats.productivityRatings.length) * 10) / 10;
      }

      // Remove the array of ratings from the final object
      delete stats.productivityRatings;
    })

    return {
      dailyStats: Object.values(dailyStats).sort((a, b) => a.date.localeCompare(b.date)),
      weeklyStats: Object.values(weeklyStats).sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year
        return a.weekNumber - b.weekNumber
      }),
      monthlyStats: Object.values(monthlyStats).sort((a, b) => a.monthKey.localeCompare(b.monthKey)),
      subjectStats: Object.values(subjectStats),
      taskTypeStats: Object.values(taskTypeStats)
    }
  }

  // Function to calculate streak data
  const calculateStreakInfo = (dailyStats: any[]): {
    currentStreak: number;
    longestStreak: number;
    streakMap: { [date: string]: boolean }; 
  } => {
    const streakMap: { [date: string]: boolean } = {}; 
    let longestStreak = 0;
    let tempCurrentStreak = 0;

    if (!dailyStats || dailyStats.length === 0) {
      return { currentStreak: 0, longestStreak: 0, streakMap };
    }

    // Ensure stats are sorted by date (oldest to newest)
    const sortedStats = [...dailyStats].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    const statsMap = new Map(sortedStats.map(stat => [stat.date, stat])); // Use a Map for efficient lookups

    let previousDate: Date | null = null;

    // Calculate longest streak
    for (const stat of sortedStats) {
      const currentDate = new Date(stat.date);
      currentDate.setHours(0, 0, 0, 0); // Normalize date

      if (stat.totalDuration > 0) {
        if (previousDate) {
          const diffTime = currentDate.getTime() - previousDate.getTime();
          const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

          if (diffDays === 1) {
            // Consecutive day
            tempCurrentStreak++;
          } else {
            // Break in streak
            longestStreak = Math.max(longestStreak, tempCurrentStreak);
            tempCurrentStreak = 1; // Start new streak
          }
        } else {
          // First study day in the sorted list
          tempCurrentStreak = 1;
        }
        previousDate = currentDate;
      } else {
        // Day with no study, breaks the streak
        longestStreak = Math.max(longestStreak, tempCurrentStreak);
        tempCurrentStreak = 0;
        previousDate = currentDate; // Still update previousDate to check the next day correctly
      }
    }
    longestStreak = Math.max(longestStreak, tempCurrentStreak); // Final check for longest streak

    // Calculate the current streak ending today (or the last study day)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    let finalCurrentStreak = 0;
    let dateToCheck = new Date(today);
    
    // Find the latest date with data, ensuring it's not in the future
    const latestDataDate = sortedStats.length > 0
      ? new Date(sortedStats[sortedStats.length - 1].date)
      : null;
    if (latestDataDate) {
      latestDataDate.setHours(0,0,0,0);
      if (latestDataDate <= today) {
          dateToCheck = latestDataDate; // Start checking from the last day with data if it's today or in the past
      }
    }

    // Iterate backwards to find the length of the current streak
    let tempDate = new Date(dateToCheck);
    while (true) {
      const dateString = formatDateToLocalYYYYMMDD(tempDate);
      const dayStat = statsMap.get(dateString);

      if (dayStat && dayStat.totalDuration > 0) {
        finalCurrentStreak++;
        tempDate.setDate(tempDate.getDate() - 1); // Move to the previous day
      } else {
        // Streak broken or no data for the day
        break;
      }
    }

    // Populate streakMap for the current streak
    if (finalCurrentStreak >= 2) {
      tempDate = new Date(dateToCheck);
      for (let i = 0; i < finalCurrentStreak; i++) {
        const dateString = formatDateToLocalYYYYMMDD(tempDate);
        streakMap[dateString] = true;
        tempDate.setDate(tempDate.getDate() - 1);
      }
    }

    return { currentStreak: finalCurrentStreak, longestStreak, streakMap };
  };

  // Helper function to calculate this month's average study time with improved accuracy
  const calculateThisMonthAverage = () => {
    if (!analytics || analytics.dailyStats.length === 0) return 0;
    
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1; // Adding 1 because JS months are 0-indexed
    
    // Filter for current month's stats
    const thisMonthStats = analytics.dailyStats.filter(day => {
      try {
        const date = new Date(day.date);
        return date.getFullYear() === currentYear && date.getMonth() + 1 === currentMonth;
      } catch (error) {
        return false;
      }
    });
    
    if (thisMonthStats.length === 0) return 0;
    
    // Calculate total duration for this month
    const totalDuration = thisMonthStats.reduce((sum, day) => sum + day.totalDuration, 0);
    
    // Get the current day of the month, or total days with data
    const today = now.getDate();
    const daysWithData = thisMonthStats.length;
    
    // Use the smaller of today and daysWithData to avoid division by full month 
    // when we only have part of the month's data
    const daysToConsider = Math.min(today, daysWithData);
    
    // Return average daily study time
    return daysToConsider > 0 ? totalDuration / daysToConsider : 0;
  };

  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) {
        navigate('/');
        return;
      }
      
      setIsLoading(true);
      try {
        // Fetch extended profile data
        const extProfile = await getExtendedUserProfile(user.id);
        setExtendedProfile(extProfile);
        setProfilePicKey(Date.now()); // Initialize key to bust cache on initial load
        
        if (extProfile) {
          setUsername(extProfile.username || "");
          setDisplayName(extProfile.displayName || user.user_metadata?.full_name || extProfile.username || "");
          setBackgroundImage(extProfile.backgroundImage || "");
          setBio(extProfile.bio || "");
          setLocation(extProfile.location || "");

          // Set edit form initial values
          setEditForm({
            username: extProfile.username || "",
            displayName: extProfile.displayName || user.user_metadata?.full_name || extProfile.username || "",
            bio: extProfile.bio || "",
            location: extProfile.location || ""
          });
        }
        
        // Fetch user stats
        setStats(extProfile?.stats || null);
        
        // Fetch study sessions data for analytics
        setIsAnalyticsLoading(true);
        try {
          // Get study sessions from the extended profile
          const studySessionsData = extProfile?.studySessions || {};
          const sessions = Object.values(studySessionsData) as StudySession[];

          // Process analytics
          const processedAnalytics = processAnalytics(sessions);
          setAnalytics(processedAnalytics);

          // Create color map for subjects - prioritize saved colors
          const colorMap: { [subject: string]: string } = {};

          // First try to extract subjectColor from sessions if available
          sessions.forEach((session) => {
            if (session.subject && session.subjectColor) {
              colorMap[session.subject] = session.subjectColor;
            }
          });

          // For any remaining subjects without colors, assign from COLORS array
          processedAnalytics.subjectStats.forEach((subjectStat, index) => {
            if (!colorMap[subjectStat.subject]) {
              colorMap[subjectStat.subject] = COLORS[index % COLORS.length];
            }
          });

          setSubjectColorMap(colorMap);

          // Calculate streak info
          const calculatedStreakInfo = calculateStreakInfo(processedAnalytics.dailyStats);
          setStreakInfo(calculatedStreakInfo);
        } catch (error) {
          console.error("Error loading analytics data:", error);
          // Set default empty values
          setAnalytics({ dailyStats: [], weeklyStats: [], monthlyStats: [], subjectStats: [], taskTypeStats: [] });
          setSubjectColorMap({});
          setStreakInfo({ currentStreak: 0, longestStreak: 0, streakMap: {} });
        } finally {
          setIsAnalyticsLoading(false);
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
        toast({
          title: "Error",
          description: "Failed to load user profile",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [user, navigate]);

  // Handle profile picture selection
  const handleProfilePicChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setProfilePicFile(e.target.files[0]);
    }
  };

  // Handle background image selection
  const handleBackgroundImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setBackgroundImageFile(e.target.files[0]);
    }
  };

  // Handle profile picture upload
  const handleProfilePicUpload = async () => {
    if (!user || !profilePicFile) return;

    setIsUploading(prev => ({ ...prev, profile: true }));
    try {
      const updatedUser = await updateUserProfilePicture(user.id, profilePicFile);

      toast({
        title: "Success",
        description: "Profile picture updated successfully",
      });

      // Update local state
      setProfilePicFile(null);

      // Update user store with new photo URL
      updateUserProfileStore({ photo_url: updatedUser.photo_url });

      // Force refresh of user data
      const extProfile = await getExtendedUserProfile(user.id);
      setExtendedProfile(extProfile);
      setProfilePicKey(Date.now()); // Update key to bust cache after successful upload
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      toast({
        title: "Error",
        description: "Failed to upload profile picture",
        variant: "destructive",
      });
    } finally {
      setIsUploading(prev => ({ ...prev, profile: false }));
    }
  };

  // Handle background image upload
  const handleBackgroundImageUpload = async () => {
    if (!user || !backgroundImageFile) return;
    
    setIsUploading(prev => ({ ...prev, background: true }));
    try {
      const backgroundURL = await updateUserBackgroundImage(user.id, backgroundImageFile);
      
      toast({
        title: "Success",
        description: "Background image updated successfully",
      });
      
      // Update local state
      setBackgroundImage(backgroundURL);
      setBackgroundImageFile(null);
    } catch (error) {
      console.error('Error uploading background image:', error);
      toast({
        title: "Error",
        description: "Failed to upload background image",
        variant: "destructive",
      });
    } finally {
      setIsUploading(prev => ({ ...prev, background: false }));
    }
  };

  const startEditing = () => {
    setEditForm({
      username,
      displayName,
      bio,
      location
    });
    setEditMode(true);
    setIsEditSheetOpen(true);
  };

  const cancelEditing = () => {
    setEditMode(false);
    setIsEditSheetOpen(false);
    setProfilePicFile(null);
    setBackgroundImageFile(null);
  };

  const handleProfileUpdate = async () => {
    if (!user) return;
    
    if (editForm.username.trim().length < 3) {
      toast({
        title: "Invalid Username",
        description: "Username must be at least 3 characters",
        variant: "destructive",
      });
      return;
    }

    setIsUpdating(true);
    try {
      // Check username availability before updating if it changed
      if (editForm.username !== extendedProfile?.username) {
        const isAvailable = await checkUsernameAvailability(editForm.username.trim());
        if (!isAvailable) {
          toast({
            title: "Error",
            description: "This username is already taken. Please choose a different one.",
            variant: "destructive",
          });
          setIsUpdating(false);
          return;
        }
      }

      // Process profile images if selected
      if (profilePicFile) {
        await handleProfilePicUpload();
      }
      
      if (backgroundImageFile) {
        await handleBackgroundImageUpload();
      }

      // Update profile with all fields
      // Update profile with text fields only; image URLs are updated by upload handlers
      const updatedProfile = await updateUserProfile(user.id, {
        username: editForm.username.trim(),
        display_name: editForm.displayName.trim() || editForm.username.trim(),
        bio: editForm.bio.trim(),
        location: editForm.location.trim()
      });

      // Update user store with new profile data
      updateUserProfileStore({
        username: editForm.username.trim(),
        display_name: editForm.displayName.trim() || editForm.username.trim(),
        bio: editForm.bio.trim(),
        location: editForm.location.trim()
      });

      // Refresh profile data
      const extProfile = await getExtendedUserProfile(user.id);
      setExtendedProfile(extProfile);

      // Update state with new values
      setUsername(editForm.username);
      setDisplayName(editForm.displayName);
      setBio(editForm.bio);
      setLocation(editForm.location);
      
      setStats(extProfile?.stats || null);

      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
      });
      
      setEditMode(false);
      setIsEditSheetOpen(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  if (!user) return null;

  return (
    <div className="container max-w-4xl py-6 sm:py-10 space-y-6 sm:space-y-8 px-3 sm:px-4">
      <div className="flex items-center gap-2">
        <Button 
          variant="ghost" 
          size="icon"
          onClick={() => navigate(-1)}
          className="rounded-full"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-2xl sm:text-3xl font-bold">Profile</h1>
      </div>

      <Card className="w-full overflow-hidden border-none shadow-md">
        {/* Background image */}
        <div 
          className="h-32 sm:h-48 relative bg-gradient-to-r from-blue-500 to-purple-600 overflow-hidden"
          style={backgroundImage ? { backgroundImage: `url(${backgroundImage})`, backgroundSize: 'cover', backgroundPosition: 'center' } : {}}
        >
          {editMode && (
            <>
              <input
                type="file"
                accept="image/*"
                className="hidden"
                ref={backgroundImageInputRef}
                onChange={handleBackgroundImageChange}
              />
              <Button 
                size="sm"
                variant="secondary"
                className="absolute bottom-3 right-3 gap-1 h-8 text-xs"
                onClick={() => backgroundImageInputRef.current?.click()}
                disabled={isUploading.background}
              >
                {isUploading.background ? (
                  <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 animate-spin mr-1" />
                ) : (
                  <Camera className="h-3 w-3 sm:h-4 sm:w-4" />
                )}
                {backgroundImageFile ? 'Change Cover' : 'Edit Cover'}
              </Button>
              {backgroundImageFile && (
                <div className="absolute bottom-3 left-3 bg-black/70 text-white px-2 py-1 rounded text-xs">
                  New background
                </div>
              )}
            </>
          )}
        </div>

        <div className="px-4 sm:px-6 pb-4 sm:pb-6 -mt-12 sm:-mt-16">
          <div className="flex flex-col sm:flex-row sm:items-end gap-3 sm:gap-4">
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                className="hidden"
                ref={profilePicInputRef}
                onChange={handleProfilePicChange}
              />
              <Avatar className="h-24 w-24 sm:h-32 sm:w-32 border-4 border-background shadow-lg">
                <AvatarImage
                  src={extendedProfile?.photoURL || user.user_metadata?.avatar_url || undefined}
                  alt={displayName || "User"}
                />
                <AvatarFallback className="text-2xl sm:text-3xl">
                  {displayName?.[0]?.toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>
              {editMode && (
                <Button 
                  size="icon" 
                  className="absolute bottom-0 right-0 h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-primary text-primary-foreground"
                  onClick={() => profilePicInputRef.current?.click()}
                  disabled={isUploading.profile}
                >
                  {isUploading.profile ? (
                    <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                  ) : (
                    <Camera className="h-3 w-3 sm:h-4 sm:w-4" />
                  )}
                </Button>
              )}
              {profilePicFile && !isUploading.profile && (
                <div className="absolute -bottom-6 left-0 w-full text-center text-xs text-muted-foreground">
                  New profile pic
                </div>
              )}
            </div>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-end flex-1 mt-3 sm:mt-0">
              <div className="bg-background/80 backdrop-blur-sm py-1 px-3 rounded-lg shadow-sm max-w-full">
                <h2 className="text-xl sm:text-2xl font-bold truncate">@{username}</h2>
                
                {/* Display user bio and location if available */}
                {bio && (
                  <div className="mt-2 text-sm max-w-full">
                    {bio.split('\n').map((line, i) => (
                      <p key={i} className={i !== 0 ? 'mt-1' : ''}>
                        {line}
                      </p>
                    ))}
                  </div>
                )}
                
                {location && (
                  <div className="flex items-center text-xs sm:text-sm text-muted-foreground mt-2">
                    <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                    <span className="truncate">{location}</span>
                  </div>
                )}
              </div>
              
              <Button 
                variant="outline" 
                onClick={startEditing}
                className="mt-3 sm:mt-0 h-8 text-xs sm:text-sm"
                size="sm"
              >
                <Edit2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                Edit Profile
              </Button>
            </div>
          </div>

          {/* Profile reputation badge if available */}
          {stats?.rank && (
            <div className="mt-3 sm:mt-4 flex flex-wrap gap-1.5 sm:gap-2">
              <Badge variant="outline" className="px-2 sm:px-3 py-0.5 sm:py-1 text-xs">
                <Award className="h-3 w-3 mr-1" />
                {stats.rank}
              </Badge>
              {stats.badges && stats.badges.map((badge, i) => (
                <Badge key={i} variant="secondary" className="px-2 sm:px-3 py-0.5 sm:py-1 text-xs">
                  {badge}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </Card>
      
      {/* Edit Profile Sheet */}
      <Sheet open={isEditSheetOpen} onOpenChange={(open) => {
        if (!open) cancelEditing();
        setIsEditSheetOpen(open);
      }}>
        <SheetContent className="sm:max-w-md overflow-y-auto pb-20 p-3 sm:p-6">
          <SheetHeader className="sticky top-0 bg-background/95 backdrop-blur-sm z-10 pb-3 sm:pb-4">
            <SheetTitle className="text-lg sm:text-xl">Edit Profile</SheetTitle>
            <SheetDescription className="text-xs sm:text-sm">Make changes to your profile information</SheetDescription>
          </SheetHeader>
          
          <div className="space-y-5 mt-4">
            {/* Profile Image */}
            <div className="flex flex-col items-center gap-3">
              <div className="relative">
                <Avatar className="h-20 w-20 sm:h-24 sm:w-24 border-2 border-background">
                  <AvatarImage
                    src={extendedProfile?.photoURL || user.user_metadata?.avatar_url || undefined}
                    alt={displayName || "User"}
                  />
                  <AvatarFallback className="text-2xl">
                    {displayName?.[0]?.toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
                <Button 
                  size="icon" 
                  className="absolute bottom-0 right-0 h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-primary text-primary-foreground shadow-md"
                  onClick={() => profilePicInputRef.current?.click()}
                  disabled={isUploading.profile}
                >
                  {isUploading.profile ? (
                    <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                  ) : (
                    <Camera className="h-3 w-3 sm:h-4 sm:w-4" />
                  )}
                </Button>
              </div>
              {profilePicFile && (
                <Badge variant="outline" className="bg-primary/10 text-xs">
                  New profile picture selected
                </Badge>
              )}
            </div>
            
            {/* Background Image */}
            <div className="space-y-1.5">
              <Label htmlFor="cover-image" className="text-sm font-medium">
                Cover Image
              </Label>
              <div 
                className="h-20 sm:h-24 w-full rounded-md bg-gradient-to-r from-blue-500 to-purple-600 overflow-hidden cursor-pointer flex items-center justify-center relative"
                style={backgroundImage ? { backgroundImage: `url(${backgroundImage})`, backgroundSize: 'cover', backgroundPosition: 'center' } : {}}
                onClick={() => backgroundImageInputRef.current?.click()}
              >
                {!backgroundImage && !backgroundImageFile && (
                  <Camera className="h-5 w-5 sm:h-6 sm:w-6 text-white/80" />
                )}
                {isUploading.background && (
                  <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                    <Loader2 className="h-5 w-5 sm:h-6 sm:w-6 animate-spin text-white" />
                  </div>
                )}
              </div>
              {backgroundImageFile && (
                <p className="text-xs text-muted-foreground">
                  New background image selected
                </p>
              )}
            </div>

            {/* Display Name */}
            <div className="space-y-1.5">
              <Label htmlFor="display-name" className="text-sm font-medium">
                Display Name
              </Label>
              <Input
                id="display-name"
                value={editForm.displayName}
                onChange={(e) => setEditForm({...editForm, displayName: e.target.value})}
                className="focus-visible:ring-primary h-9"
                placeholder="Enter your display name"
              />
            </div>
            
            {/* Username */}
            <div className="space-y-1.5">
              <Label htmlFor="username" className="text-sm font-medium">
                Username
              </Label>
              <div className="flex items-center border rounded-md overflow-hidden focus-within:ring-1 focus-within:ring-primary">
                <span className="text-muted-foreground pl-3">@</span>
                <Input
                  id="username"
                  value={editForm.username}
                  onChange={(e) => setEditForm({...editForm, username: e.target.value})}
                  className="border-0 focus-visible:ring-0 h-9"
                  placeholder="username"
                />
              </div>
            </div>
            
            {/* Bio */}
            <div className="space-y-1.5">
              <Label htmlFor="bio" className="text-sm font-medium">
                Bio
              </Label>
              <Textarea
                id="bio"
                value={editForm.bio}
                onChange={(e) => setEditForm({...editForm, bio: e.target.value})}
                className="resize-none min-h-[80px] sm:min-h-[100px]"
                placeholder="Tell others about yourself... (Line breaks will be preserved)"
              />
            </div>
            
            {/* Location */}
            <div className="space-y-1.5">
              <Label htmlFor="location" className="text-sm font-medium">
                Location
              </Label>
              <div className="flex items-center border rounded-md overflow-hidden focus-within:ring-1 focus-within:ring-primary">
                <MapPin className="h-4 w-4 text-muted-foreground ml-3" />
                <Input
                  id="location"
                  value={editForm.location}
                  onChange={(e) => setEditForm({...editForm, location: e.target.value})}
                  className="border-0 focus-visible:ring-0 h-9"
                  placeholder="City, Country"
                />
              </div>
            </div>
          </div>
          
          <div className="flex justify-end gap-2 mt-6 sticky bottom-0 pt-3 sm:pt-4 px-3 -mx-3 sm:px-6 sm:-mx-6 bg-background/95 backdrop-blur-sm border-t">
            <Button 
              variant="outline" 
              onClick={cancelEditing}
              className="rounded-full gap-1 h-9 text-xs"
              size="sm"
            >
              <X className="h-3.5 w-3.5" />
              <span className="sm:inline">Cancel</span>
            </Button>
            <Button 
              onClick={handleProfileUpdate}
              disabled={isUpdating}
              className="rounded-full bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 gap-1 h-9 text-xs"
              size="sm"
            >
              {isUpdating ? (
                <Loader2 className="h-3.5 w-3.5 animate-spin" />
              ) : (
                <Save className="h-3.5 w-3.5" />
              )}
              <span className="sm:inline">Save Changes</span>
            </Button>
          </div>
        </SheetContent>
      </Sheet>
      
      <Tabs defaultValue="overview" className="w-full" onValueChange={setActiveTab} value={activeTab}>
        <div className="flex justify-center mb-4 sm:mb-8 overflow-x-auto px-1 py-1">
          <TabsList className="bg-card/60 backdrop-blur-sm border border-border/40 p-1 rounded-full shadow-sm">
            <TabsTrigger 
              value="overview"
              className="px-4 sm:px-8 py-1.5 sm:py-2 text-xs sm:text-sm rounded-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger 
              value="stats"
              className="px-4 sm:px-8 py-1.5 sm:py-2 text-xs sm:text-sm rounded-full data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
            >
              Statistics
            </TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="overview" className="space-y-4 sm:space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <Card>
              <CardHeader className="pb-2 sm:pb-3">
                <CardTitle className="text-base sm:text-lg">User Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 sm:space-y-4 text-sm">
                <div>
                  <Label className="text-xs sm:text-sm text-muted-foreground">Username</Label>
                  <p className="truncate">@{username}</p>
                </div>
                <Separator />
                <div>
                  <Label className="text-xs sm:text-sm text-muted-foreground">Member Since</Label>
                  <p>{new Date(extendedProfile?.member_since || extendedProfile?.created_at || Date.now()).toLocaleDateString()}</p>
                </div>
                <Separator />
                <div>
                  <Label className="text-xs sm:text-sm text-muted-foreground">Last Login</Label>
                  <p>{new Date(extendedProfile?.last_login || extendedProfile?.lastLogin || Date.now()).toLocaleDateString()}</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2 sm:pb-3">
                <CardTitle className="text-base sm:text-lg">Study Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 sm:space-y-4">
                {isAnalyticsLoading ? (
                  <div className="flex justify-center items-center h-20">
                    <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                  </div>
                ) : !analytics ? (
                  <div className="text-center py-2">
                    <p className="text-muted-foreground text-sm">No study data available</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-3 sm:gap-4 mt-2">
                    <div>
                      <p className="text-xs sm:text-sm text-muted-foreground">Study Streak</p>
                      <p className="text-lg sm:text-2xl font-bold">{streakInfo.currentStreak}</p>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Award className="h-3 w-3 mr-1" />
                        <span>Longest: {streakInfo.longestStreak}</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-xs sm:text-sm text-muted-foreground">Total Study Time</p>
                      <p className="text-lg sm:text-2xl font-bold">
                        {formatDuration(analytics.dailyStats.reduce((sum, day) => sum + day.totalDuration, 0))}
                      </p>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Activity className="h-3 w-3 mr-1" />
                        <span>
                          Avg: {formatDuration(calculateThisMonthAverage())}/day
                        </span>
                      </div>
                    </div>
                    <div>
                      <p className="text-xs sm:text-sm text-muted-foreground">Pomodoros Completed</p>
                      <p className="text-lg sm:text-2xl font-bold">
                        {analytics.dailyStats.reduce((sum, day) => sum + day.completedPomodoros, 0)}
                      </p>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Timer className="h-3 w-3 mr-1" />
                        <span>
                          {analytics.taskTypeStats?.length || 0} task types
                        </span>
                      </div>
                    </div>
                    <div>
                      <p className="text-xs sm:text-sm text-muted-foreground">Questions Asked</p>
                      <p className="text-lg sm:text-2xl font-bold">{stats?.questionsAsked || 0}</p>
                      <div className="flex items-center text-sm text-muted-foreground">
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="stats" className="space-y-4 sm:space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Study Analytics</CardTitle>
              <CardDescription>Your learning progress and statistics</CardDescription>
            </CardHeader>
            <CardContent>
              {isAnalyticsLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : !analytics || analytics.dailyStats.length === 0 ? (
                <div className="text-center py-8">
                  <div className="bg-muted/50 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                    <Clock className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No Study Data Yet</h3>
                  <p className="text-muted-foreground max-w-md mx-auto">
                    You haven't tracked any study sessions yet. Start a study session in the Timer section to see your analytics.
                  </p>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-6">
                    <div className="space-y-1">
                      <p className="text-muted-foreground text-xs sm:text-sm">Current Streak</p>
                      <div className="flex items-center">
                        <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-primary" />
                        <p className="text-lg sm:text-2xl font-bold">{streakInfo.currentStreak}</p>
                        <p className="text-muted-foreground ml-1 text-xs sm:text-sm">days</p>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <p className="text-muted-foreground text-xs sm:text-sm">Longest Streak</p>
                      <div className="flex items-center">
                        <Award className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-primary" />
                        <p className="text-lg sm:text-2xl font-bold">{streakInfo.longestStreak}</p>
                        <p className="text-muted-foreground ml-1 text-xs sm:text-sm">days</p>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <p className="text-muted-foreground text-xs sm:text-sm">Completed Pomodoros</p>
                      <div className="flex items-center">
                        <Timer className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-primary" />
                        <p className="text-lg sm:text-2xl font-bold">
                          {analytics.dailyStats.reduce((sum, day) => sum + day.completedPomodoros, 0)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <p className="text-muted-foreground text-xs sm:text-sm">This Month Avg.</p>
                      <div className="flex items-center">
                        <Activity className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 text-primary" />
                        <p className="text-lg sm:text-2xl font-bold">
                          {formatDuration(calculateThisMonthAverage())}
                        </p>
                        <p className="text-muted-foreground ml-1 text-xs sm:text-sm">/day</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-4 mb-2 bg-muted/30 rounded-lg p-3 sm:p-4 border">
                    <div className="flex items-center">
                      <Clock className="h-5 w-5 mr-2 text-primary" />
                      <h3 className="text-base font-medium">Total Study Time</h3>
                    </div>
                    <p className="text-lg sm:text-xl font-bold">
                      {formatDuration(analytics.dailyStats.reduce((sum, day) => sum + day.totalDuration, 0))}
                    </p>
                  </div>

                  {/* This month's total study time */}
                  {analytics.dailyStats.length > 0 && (
                    <div className="flex justify-between items-center mb-2 bg-muted/30 rounded-lg p-3 sm:p-4 border">
                      <div className="flex items-center">
                        <Calendar className="h-5 w-5 mr-2 text-primary" />
                        <h3 className="text-base font-medium">
                          {new Date().toLocaleDateString('en-US', { month: 'long' })} Total
                        </h3>
                      </div>
                      <p className="text-lg sm:text-xl font-bold">
                        {formatDuration(
                          analytics.dailyStats
                            .filter(day => {
                              try {
                                const date = new Date(day.date);
                                const now = new Date();
                                return date.getMonth() === now.getMonth() && 
                                       date.getFullYear() === now.getFullYear();
                              } catch (error) {
                                return false;
                              }
                            })
                            .reduce((sum, day) => sum + day.totalDuration, 0)
                        )}
                      </p>
                    </div>
                  )}

                  <Separator className="my-4 sm:my-6" />

                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-base sm:text-lg font-medium">Study Time by Subject</h3>
                      <Badge variant="outline" className="font-normal">
                        {analytics.subjectStats.length} subjects
                      </Badge>
                    </div>
                    
                    <div className="space-y-2 sm:space-y-3">
                      {analytics.subjectStats
                        .sort((a, b) => b.totalDuration - a.totalDuration)
                        .slice(0, 5)
                        .map((subject, index) => {
                          const totalDuration = analytics.subjectStats.reduce((sum, s) => sum + s.totalDuration, 0);
                          const percentage = totalDuration > 0 ? (subject.totalDuration / totalDuration) * 100 : 0;
                          
                          return (
                            <div key={subject.subject}>
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-1 sm:gap-2">
                                  <div 
                                    className="w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full" 
                                    style={{ backgroundColor: subjectColorMap[subject.subject] || COLORS[index % COLORS.length] }}
                                  ></div>
                                  <span className="text-sm">{subject.subject}</span>
                                </div>
                                <span className="font-medium text-sm">{formatDuration(subject.totalDuration)}</span>
                              </div>
                              <div className="w-full bg-muted rounded-full h-2 sm:h-2.5 mt-1">
                                <div 
                                  className="h-2 sm:h-2.5 rounded-full" 
                                  style={{ 
                                    width: `${percentage}%`,
                                    backgroundColor: subjectColorMap[subject.subject] || COLORS[index % COLORS.length]
                                  }}
                                ></div>
                              </div>
                            </div>
                          );
                        })}
                        
                      {analytics.subjectStats.length > 5 && (
                        <div className="text-center mt-2">
                          <Badge variant="secondary" className="font-normal">
                            +{analytics.subjectStats.length - 5} more subjects
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>

                  <Separator className="my-4 sm:my-6" />
                  
                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-base sm:text-lg font-medium">Study Session Types</h3>
                      <Badge variant="outline" className="font-normal">
                        By task type
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {analytics.taskTypeStats && analytics.taskTypeStats
                        .sort((a, b) => b.totalDuration - a.totalDuration)
                        .slice(0, 6)
                        .map((taskType, index) => {
                          const totalTime = analytics.taskTypeStats?.reduce((sum, t) => sum + t.totalDuration, 0) || 0;
                          const percentage = totalTime > 0 ? Math.round((taskType.totalDuration / totalTime) * 100) : 0;
                          
                          return (
                            <div key={taskType.taskType} className="bg-card/60 backdrop-blur-sm border rounded-lg p-3 shadow-sm">
                              <div className="flex justify-between items-center mb-2">
                                <span className="font-medium">{taskType.taskType}</span>
                                <Badge variant="secondary" className="font-normal">
                                  {percentage}%
                                </Badge>
                              </div>
                              <div className="flex items-center justify-between text-sm text-muted-foreground mb-1">
                                <span>Total: {formatDuration(taskType.totalDuration)}</span>
                                <span>Sessions: {taskType.sessionCount}</span>
                              </div>
                              <div className="w-full bg-muted rounded-full h-1.5">
                                <div 
                                  className="h-1.5 rounded-full bg-primary/70" 
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                            </div>
                          );
                        })}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProfilePage; 